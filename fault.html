<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>故障管理 - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/bigalarm.css" />
    <link rel="stylesheet" href="css/fault.css" />
    <script src="js/tabs.js" defer></script>
    <script src="js/xlsx.js"></script>
    <script src="js/fault.js" defer></script>
    <script src="js/layui.js"></script>
    <script src="js/echarts.js"></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'search':
                        switchTab('search');
                        break;
                    case 'register':
                        switchTab('register');
                        break;
                    default:
                        switchTab('search');
                }
            }
        });
    </script>
  </head>
  <body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0;">
      <div class="tabs" style="display: none;">
        <div class="tabs-left">
          <button class="tab-button active" onclick="switchTab('search')">
            故障分析
          </button>
          <button class="tab-button" onclick="switchTab('register')">
            故障列表
          </button>
        </div>
      </div>

      <!-- 故障分析选项卡 -->
      <div id="searchTab" class="tab-content active">

        <!-- 添加图表区域 -->
        <div class="fault-charts" id="faultCharts">
          <div class="chart-row">
            <div class="faultchart-container">
              <div id="chart-monthly" style="width: 100%; height: 300px;"></div>
            </div>
            <div class="faultchart-container">
              <div id="chart-weekly" style="width: 100%; height: 300px;"></div>
            </div>
            <div class="faultchart-container">
              <div id="chart-daily" style="width: 100%; height: 300px;"></div>
            </div>
          </div>
          <div class="chart-row">
            <div class="faultchart-container">
              <div id="chart-section-fault" style="width: 100%; height: 300px;"></div>
            </div>
            <div class="faultchart-container">
              <div id="chart-unit-loss" style="width: 100%; height: 300px;"></div>
            </div>
            <div class="faultchart-container">
              <div id="chart-line-fault" style="width: 100%; height: 300px;"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 故障列表选项卡 -->
      <div id="registerTab" class="tab-content">
        <div class="fault-search">
          <form id="searchForm" action="#" method="get">
            <div class="search-row">
              <label for="search-section">科室</label>
              <select id="search-section" name="section">
                <option value="">ALL</option>
              </select>

              <label for="search-line">LINE</label>
              <select id="search-line" name="line"></select>

              <label for="search-unit">UNIT</label>
              <select id="search-unit" name="unit"></select>

              <label for="search-category">故障/品质</label>
              <select id="search-category" name="category">
                <option value="">ALL</option>
                <option value="故障">故障</option>
                <option value="品质">品质</option>
              </select>

              <label for="search-keyword">关键词</label>
              <input
                type="text"
                id="search-keyword"
                name="keyword"
                placeholder="请输入关键词查询"
              />
              <label for="search-start-date">日期</label>
              <input type="date" id="search-start-date" name="start_date" />

              <label for="search-end-date">~</label>
              <input type="date" id="search-end-date" name="end_date" />

              <div class="button-group">
                <button type="submit" class="search-btn">查询</button>
                <button type="reset" class="reset-btn">重置</button>
                <button type="download" class="download-btn">导出</button>
                <button type="add" class="add-btn">新增</button>
              </div>
            </div>
          </form>
        </div>

        <div class="fault-list">
          <table class="data-table">
            <thead>
              <tr>
                <th>日期</th>
                <th>LOSS</th>
                <th>问题点</th>
                <th>发生现象</th>
                <th>改善</th>
                <th>科室</th>
                <th>LINE</th>
                <th>UNIT</th>
                <th>分类</th>
                <th>方向</th>
                <!-- <th>关键词</th> -->
                <!-- <th>发生时间</th> -->
                <th>状态</th>
                <th>改善担当</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <!-- 数据将通过 JavaScript 动态加载 -->
            </tbody>
          </table>
        </div>

        <!-- 在表格后添加分页控件 -->
        <div class="pagination">
          <div class="pagination-info">
            共 <span class="total-count">0</span> 条记录， 每页
            <select class="page-size">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
            </select>
            条
          </div>
          <div class="pagination-controls">
            <button class="btn-first-page">首页</button>
            <button class="btn-prev-page">上一页</button>
            <span class="page-info">
              第 <input type="number" class="current-page" min="1" /> 页， 共
              <span class="total-pages">0</span> 页
            </span>
            <button class="btn-next-page">下一页</button>
            <button class="btn-last-page">末页</button>
          </div>
        </div>

        <!-- <div class="fault-register">
          <form action="#" method="post" class="register-form">
            <div class="form-container">
              <div class="left-column">
                <div class="form-group">
                  <label for="register-section">科室</label>
                  <select id="register-section" name="section" required disabled>
                    <option value="">请选择</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="register-line">LINE</label>
                  <select id="register-line" name="line" required>
                    <option value="">请选择</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="register-unit">UNIT</label>
                  <select id="register-unit" name="unit" required>
                    <option value="">请选择</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="register-category">故障/品质</label>
                  <select id="register-category" name="category" required>
                    <option value="">请选择</option>
                    <option value="故障">故障</option>
                    <option value="品质">品质</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="register-direction">硬件/软件</label>
                  <select id="register-direction" name="direction" required>
                    <option value="">请选择</option>
                    <option value="硬件">硬件</option>
                    <option value="软件">软件</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="register-keyword">关键词</label>
                  <input type="text" id="register-keyword" name="keyword" />
                </div>

                <div class="form-group">
                  <label for="register-datetime">发生时间</label>
                  <input
                    type="datetime-local"
                    id="register-datetime"
                    name="datetime"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="register-closetime">结束时间</label>
                  <input
                    type="datetime-local"
                    id="register-closetime"
                    name="closetime"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="register-status">问题状态</label>
                  <select id="register-status" name="status" required>
                    <option value="">请选择</option>
                    <option value="CLOSE">CLOSE</option>
                    <option value="OPEN">OPEN</option>
                  </select>
                </div>


                <div class="form-group">
                  <label for="register-responsible">改善担当</label>
                  <input
                    type="text"
                    id="register-responsible"
                    name="responsible"
                    required
                  />
                </div>

                <div class="form-group" style="display: none;">
                  <label for="register-recorder">录入人</label>
                  <input
                    type="text"
                    id="register-recorder"
                    name="recorder"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="register-relatedParts">相关备品</label>
                  <input
                    type="text"
                    id="register-relatedParts"
                    name="relatedParts"
                  />
                </div>

                <div class="form-group">
                  <label for="register-model">型号</label>
                  <input type="text" id="register-model" name="model" />
                </div>

                <div class="form-group">
                  <label>附件上传（图片、文档、PPT 等）</label>
                  <div class="upload-area">
                    <div class="file-upload-container">
                      <input
                        type="file"
                        id="fileUpload"
                        name="files[]"
                        multiple
                      />
                      <div class="file-list">
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="right-column">
                <div class="form-group">
                  <label for="register-issue">问题点</label>
                  <input
                    type="text"
                    id="register-issue"
                    name="issue"
                    class="full-width"
                  />
                </div>

                <div class="form-group">
                  <label for="register-phenomenon">发生现象</label>
                  <textarea
                    id="register-phenomenon"
                    name="phenomenon"
                    rows="4"
                  ></textarea>
                </div>

                <div class="form-group">
                  <label for="register-analysis">原因分析</label>
                  <textarea
                    id="register-analysis"
                    name="analysis"
                    rows="4"
                  ></textarea>
                </div>

                <div class="form-group">
                  <label for="register-measures">改善措施</label>
                  <textarea
                    id="register-measures"
                    name="measures"
                    rows="4"
                  ></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn-submit" >提交</button>
                    <p style="color: red;">*大故障Loss Time定义大于60分钟，瞬间故障请跳转 <a>交接登录</a> 页面</p>
                </div>
              </div>
              
            </div>
          </form>
        </div> -->
      </div>
    </div>

    <!-- 在故障列表表格后添加弹窗结构 -->
    <div id="faultDetailModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>1SCP01 LOADER LIFT 掉电</h2>
          <span class="close">&times;</span>
        </div>
        <div class="modal-body">
          <table class="detail-table">
            <tr>
              <th>科室</th>
              <th>LINE</th>
              <th>UNIT</th>
              <th>分类</th>
              <th>关键词</th>
              <th>改善方向</th>
              <th>相关备品</th>
              <th>型号</th>
              <th>发生时间</th>
              <th>问题状态</th>
              <th>改善担当</th>
            </tr>
            <tr></tr>
          </table>

          <div class="detail-sections">
            <div class="detail-section">
              <h3>问题现象</h3>
              <div class="section-content"></div>
            </div>

            <div class="detail-section">
              <h3>原因分析</h3>
              <div class="section-content"></div>
            </div>

            <div class="detail-section">
              <h3>改善措施</h3>
              <div class="section-content"></div>
            </div>

            <div class="detail-section">
              <h3>相关附件</h3>
              <div class="section-content file-section">
                <!-- 附件将在这里动态显示 -->
              </div>
            </div>
          </div>
          <div class="change-status">
            <h3>问题状态</h3>
            <label>
              <input type="radio" name="status" value="OPEN" required />
              <span>OPEN</span>
            </label>
            <label>
              <input type="radio" name="status" value="CLOSE" required />
              <span>CLOSE</span>
            </label>
          </div>

          <div class="modal-footer">
            <button class="btn-return">返回</button>
            <button class="btn-modify">编辑</button>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
