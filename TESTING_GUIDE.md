# 故障管理模态框功能测试指南

## 测试前准备

1. 确保Web服务器正在运行（如XAMPP）
2. 确保数据库连接正常
3. 确保用户已登录系统

## 测试步骤

### 1. 基本功能测试

#### 1.1 打开模态框
- 访问故障管理页面 (`fault.html`)
- 点击"新增"按钮
- **预期结果**：模态框弹出，显示完整的故障登录表单

#### 1.2 关闭模态框
- 点击模态框右上角的"×"按钮
- 或点击"取消"按钮
- 或点击模态框外部区域
- 或按ESC键
- **预期结果**：模态框关闭，页面恢复正常

### 2. 表单功能测试

#### 2.1 自动填充测试
- 打开模态框
- **预期结果**：
  - 科室字段自动填充用户所属科室且不可编辑
  - 录入人字段自动填充用户姓名且不可编辑

#### 2.2 选项联动测试
- 选择不同的LINE
- **预期结果**：UNIT选项根据选择的LINE动态更新

#### 2.3 表单验证测试
- 填写发生时间和结束时间，确保时间差小于60分钟
- 点击提交
- **预期结果**：显示错误提示"大故障登录LOSS TIME须大于60分钟"

#### 2.4 必填字段验证
- 留空必填字段（如LINE、UNIT等）
- 点击提交
- **预期结果**：浏览器显示必填字段验证提示

### 3. 文件上传测试

#### 3.1 文件选择
- 点击文件上传区域
- 选择一个或多个文件
- **预期结果**：文件列表显示选择的文件，包含文件名和大小

#### 3.2 文件预览
- 点击已上传的图片文件名
- **预期结果**：在新窗口中预览图片

#### 3.3 文件删除
- 点击文件项右侧的"×"按钮
- **预期结果**：文件从列表中移除

### 4. 数据提交测试

#### 4.1 成功提交
- 填写完整的表单信息（确保LOSS TIME > 60分钟）
- 点击提交
- **预期结果**：
  - 显示加载提示
  - 提交成功后显示成功提示
  - 模态框关闭
  - 故障列表自动刷新显示新添加的记录

#### 4.2 重复提交防护
- 快速连续点击提交按钮
- **预期结果**：只执行一次提交，按钮在提交过程中被禁用

### 5. 响应式设计测试

#### 5.1 桌面端测试
- 在桌面浏览器中测试
- **预期结果**：表单以两列布局显示，界面美观

#### 5.2 移动端测试
- 在移动设备或缩小浏览器窗口测试
- **预期结果**：表单自动调整为单列布局，适应小屏幕

### 6. 兼容性测试

#### 6.1 浏览器兼容性
- 在Chrome、Firefox、Safari、Edge等浏览器中测试
- **预期结果**：所有功能正常工作

#### 6.2 数据兼容性
- 提交的数据应与原有系统完全兼容
- **预期结果**：数据正确保存到数据库，格式与原系统一致

## 常见问题排查

### 问题1：模态框不显示
- 检查JavaScript控制台是否有错误
- 确认CSS文件正确加载
- 检查用户是否已登录

### 问题2：表单提交失败
- 检查网络连接
- 确认PHP后端服务正常
- 检查数据库连接
- 查看浏览器开发者工具的Network标签页

### 问题3：文件上传失败
- 检查文件大小是否超出限制
- 确认文件类型是否被允许
- 检查服务器上传目录权限

### 问题4：选项联动不工作
- 检查get_options.php接口是否正常
- 确认数据库中有相关数据
- 检查JavaScript控制台错误

## 性能测试

### 1. 加载速度
- 模态框应在点击后立即显示
- 选项数据加载应在2秒内完成

### 2. 文件上传
- 小文件（<1MB）应在5秒内上传完成
- 大文件（1-10MB）应显示上传进度

### 3. 表单提交
- 表单提交应在10秒内完成
- 超时应显示适当的错误信息

## 测试完成标准

✅ 所有基本功能正常工作
✅ 表单验证按预期执行
✅ 文件上传功能正常
✅ 数据成功保存到数据库
✅ 响应式设计在不同设备上正常显示
✅ 无JavaScript错误
✅ 用户体验流畅

## 报告问题

如果发现任何问题，请记录：
1. 问题描述
2. 重现步骤
3. 预期结果 vs 实际结果
4. 浏览器和版本信息
5. 错误截图或控制台错误信息
