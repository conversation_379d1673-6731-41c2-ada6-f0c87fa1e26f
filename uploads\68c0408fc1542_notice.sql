-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： 127.0.0.1
-- 生成日期： 2025-06-15 10:41:37
-- 服务器版本： 10.4.32-MariaDB
-- PHP 版本： 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `equipment_management`
--

-- --------------------------------------------------------

--
-- 表的结构 `notice`
--

CREATE TABLE `notice` (
  `id` int(11) NOT NULL,
  `related` varchar(50) NOT NULL COMMENT '关联事项',
  `content` text NOT NULL COMMENT '通知内容',
  `display_days` int(11) NOT NULL COMMENT '展示天数',
  `publisher` varchar(50) NOT NULL COMMENT '发布人',
  `section` varchar(50) NOT NULL COMMENT '科室',
  `project` varchar(50) NOT NULL COMMENT '工程',
  `upload_time` datetime NOT NULL COMMENT '上传时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知表';

--
-- 转存表中的数据 `notice`
--

INSERT INTO `notice` (`id`, `related`, `content`, `display_days`, `publisher`, `section`, `project`, `upload_time`, `end_time`) VALUES
(1, '故障', 'AG通知', 1, '张新宇', '检测二科', 'AG', '2025-01-09 15:15:47', '2025-01-10 15:15:47'),
(3, '故障', 'AM通知', 1, '张新宇', '检测二科', 'AM', '2025-01-20 14:11:03', '2025-01-21 14:11:03'),
(5, '故障', '123123', 1, '张新宇', '偏贴实装一科', '', '2025-02-06 12:17:57', '2025-02-07 12:17:57'),
(7, '故障', '123123', 3, '郁程阳', '偏贴实装二科', '', '2025-02-06 12:52:55', '2025-02-09 12:52:55'),
(9, '故障', '文件', 1, '张新宇', '偏贴实装一科', '', '2025-02-11 14:30:42', '2025-02-12 14:30:42'),
(10, '故障', '22', 1, '张新宇', '偏贴实装一科', '', '2025-02-11 15:11:39', '2025-02-12 15:11:39'),
(12, '故障', 'CP通知', 1, '张新宇', '偏贴实装一科', 'CP', '2025-02-18 15:21:17', '2025-02-19 15:21:17'),
(13, '故障', 'OLB通知', 1, '张新宇', '偏贴实装一科', 'OLB', '2025-02-18 15:21:36', '2025-02-19 15:21:36'),
(14, '故障', 'all通知', 1, '张新宇', '偏贴实装一科', '', '2025-02-18 15:22:27', '2025-02-19 15:22:27');

--
-- 转储表的索引
--

--
-- 表的索引 `notice`
--
ALTER TABLE `notice`
  ADD PRIMARY KEY (`id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `notice`
--
ALTER TABLE `notice`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
